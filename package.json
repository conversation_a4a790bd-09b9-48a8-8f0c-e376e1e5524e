{"name": "80lv-pagination-test", "version": "1.0.0", "description": "Playwright 自动化测试脚本用于测试 80.lv 文章页面分页导航功能", "main": "test-pagination-navigation.spec.js", "scripts": {"test": "playwright test", "test:headed": "playwright test --headed", "test:debug": "playwright test --debug", "test:ui": "playwright test --ui", "test:report": "playwright show-report", "install:browsers": "playwright install", "verify": "node verify-setup.js", "setup": "npm install && npm run install:browsers", "start": "node run-test.js"}, "keywords": ["playwright", "automation", "testing", "pagination", "80.lv"], "author": "Your Name", "license": "MIT", "devDependencies": {"@playwright/test": "^1.40.0"}, "engines": {"node": ">=16.0.0"}}