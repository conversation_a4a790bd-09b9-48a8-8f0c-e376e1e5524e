# 🚀 快速开始指南

这是一个简化的快速开始指南，帮助您立即运行 80.lv 分页导航的 Playwright 自动化测试。

## ⚡ 一键运行（推荐）

### Windows 用户
```bash
# 双击运行批处理文件
run-test.bat
```

### Linux/Mac 用户
```bash
# 运行交互式测试菜单
npm start
```

## 📋 分步骤设置

### 1. 验证环境
```bash
# 检查所有必要文件和依赖项
npm run verify
```

### 2. 安装依赖项和浏览器
```bash
# 一键安装所有必要组件
npm run setup
```

### 3. 运行测试
```bash
# 运行所有测试（无头模式）
npm test

# 运行测试并显示浏览器窗口
npm run test:headed

# 调试模式
npm run test:debug
```

## 🎯 测试内容

### 主要测试用例

1. **分页导航功能测试**
   - ✅ 验证当前页面状态
   - ✅ 导航到第二页
   - ✅ 验证"上一页"按钮功能
   - ✅ 验证页面内容更新

2. **快捷键导航测试**
   - ✅ 右箭头键（→）导航
   - ✅ 左箭头键（←）导航

3. **超快速跳转测试**
   - ✅ Alt+g 快捷键聚焦
   - ✅ 页面跳转功能

## 🔧 常用命令

```bash
# 验证环境设置
npm run verify

# 完整设置（安装依赖 + 浏览器）
npm run setup

# 运行测试（显示浏览器）
npm run test:headed

# 调试模式
npm run test:debug

# UI 模式
npm run test:ui

# 查看测试报告
npm run test:report

# 交互式菜单
npm start
```

## 📊 测试结果

测试完成后，您将看到：
- ✅ 详细的测试步骤日志
- 📸 失败时的自动截图
- 🎥 失败时的视频录制
- 📋 HTML 格式的测试报告

## 🆘 故障排除

### 常见问题

1. **"找不到 Node.js"**
   - 安装 Node.js 16.0.0 或更高版本
   - 下载地址：https://nodejs.org/

2. **"依赖项安装失败"**
   ```bash
   npm cache clean --force
   npm install
   ```

3. **"浏览器未安装"**
   ```bash
   npm run install:browsers
   ```

4. **"测试超时"**
   - 检查网络连接
   - 确保能访问 80.lv 网站

### 获取帮助

1. 运行环境验证：`npm run verify`
2. 查看详细日志：使用 `npm run test:headed` 观察浏览器行为
3. 使用调试模式：`npm run test:debug`

## 📁 文件结构

```
├── 80.lv Articles Page Navigation.js  # 原始导航脚本
├── test-pagination-navigation.spec.js # 测试文件
├── playwright.config.js               # 配置文件
├── package.json                       # 项目配置
├── run-test.js                        # 交互式运行器
├── run-test.bat                       # Windows 批处理
├── verify-setup.js                    # 环境验证
├── README.md                          # 详细文档
└── QUICK_START.md                     # 本文件
```

## 🎉 成功标志

当您看到以下输出时，表示测试成功：

```
✅ 所有测试步骤完成，分页导航功能正常工作！
✅ 快捷键导航功能测试完成
✅ 超快速跳转功能测试完成
```

---

**需要更多帮助？** 查看 [README.md](README.md) 获取详细文档。
