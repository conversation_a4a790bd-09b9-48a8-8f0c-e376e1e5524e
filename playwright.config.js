// @ts-check
const { defineConfig, devices } = require('@playwright/test');

/**
 * @see https://playwright.dev/docs/test-configuration
 */
module.exports = defineConfig({
  testDir: './',
  /* 并行运行测试 */
  fullyParallel: false,
  /* 失败时不重试 */
  retries: 0,
  /* 选择并行工作进程数量 */
  workers: 1,
  /* 报告器配置 */
  reporter: [
    ['html'],
    ['list'],
    ['json', { outputFile: 'test-results.json' }]
  ],
  /* 全局测试配置 */
  use: {
    /* 基础URL */
    baseURL: 'https://80.lv',
    
    /* 在失败时收集跟踪信息 */
    trace: 'on-first-retry',
    
    /* 截图配置 */
    screenshot: 'only-on-failure',
    
    /* 视频录制 */
    video: 'retain-on-failure',
    
    /* 浏览器上下文选项 */
    viewport: { width: 1280, height: 720 },
    
    /* 忽略HTTPS错误 */
    ignoreHTTPSErrors: true,
    
    /* 用户代理 */
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    
    /* 超时设置 */
    actionTimeout: 30000,
    navigationTimeout: 30000,
  },

  /* 配置不同浏览器的项目 */
  projects: [
    {
      name: 'chromium',
      use: { 
        ...devices['Desktop Chrome'],
        // 启用开发者工具以便调试
        launchOptions: {
          // headless: false, // 取消注释以查看浏览器操作
          // slowMo: 1000,    // 取消注释以减慢操作速度
        }
      },
    },

    // 可以添加其他浏览器测试
    // {
    //   name: 'firefox',
    //   use: { ...devices['Desktop Firefox'] },
    // },

    // {
    //   name: 'webkit',
    //   use: { ...devices['Desktop Safari'] },
    // },
  ],

  /* 在本地开发服务器启动前运行 */
  // webServer: {
  //   command: 'npm run start',
  //   url: 'http://127.0.0.1:3000',
  //   reuseExistingServer: !process.env.CI,
  // },
});
