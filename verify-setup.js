#!/usr/bin/env node

/**
 * 测试环境验证脚本
 * 验证所有必要的文件和依赖项是否正确设置
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  white: '\x1b[37m'
};

function log(color, message, symbol = '') {
  console.log(`${colors[color]}${symbol} ${message}${colors.reset}`);
}

function success(message) {
  log('green', message, '✅');
}

function error(message) {
  log('red', message, '❌');
}

function warning(message) {
  log('yellow', message, '⚠️ ');
}

function info(message) {
  log('blue', message, 'ℹ️ ');
}

function header(message) {
  console.log('\n' + '='.repeat(60));
  log('cyan', message, '🔍');
  console.log('='.repeat(60));
}

// 检查 Node.js 版本
function checkNodeVersion() {
  try {
    const version = process.version;
    const majorVersion = parseInt(version.slice(1).split('.')[0]);
    
    if (majorVersion >= 16) {
      success(`Node.js 版本: ${version} (符合要求)`);
      return true;
    } else {
      error(`Node.js 版本: ${version} (需要 16.0.0 或更高版本)`);
      return false;
    }
  } catch (e) {
    error('无法检查 Node.js 版本');
    return false;
  }
}

// 检查必要文件
function checkRequiredFiles() {
  const requiredFiles = [
    {
      name: '80.lv Articles Page Navigation.js',
      description: '原始导航脚本'
    },
    {
      name: 'test-pagination-navigation.spec.js',
      description: 'Playwright 测试文件'
    },
    {
      name: 'playwright.config.js',
      description: 'Playwright 配置文件'
    },
    {
      name: 'package.json',
      description: '项目配置文件'
    },
    {
      name: 'README.md',
      description: '说明文档'
    }
  ];

  let allFilesExist = true;

  requiredFiles.forEach(file => {
    if (fs.existsSync(path.join(__dirname, file.name))) {
      success(`${file.name} - ${file.description}`);
    } else {
      error(`缺少文件: ${file.name} - ${file.description}`);
      allFilesExist = false;
    }
  });

  return allFilesExist;
}

// 检查 package.json 内容
function checkPackageJson() {
  try {
    const packagePath = path.join(__dirname, 'package.json');
    if (!fs.existsSync(packagePath)) {
      error('package.json 文件不存在');
      return false;
    }

    const packageContent = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    
    // 检查必要的脚本
    const requiredScripts = ['test', 'test:headed', 'test:debug', 'install:browsers'];
    const missingScripts = requiredScripts.filter(script => !packageContent.scripts || !packageContent.scripts[script]);
    
    if (missingScripts.length === 0) {
      success('package.json 包含所有必要的脚本');
    } else {
      warning(`package.json 缺少脚本: ${missingScripts.join(', ')}`);
    }

    // 检查依赖项
    if (packageContent.devDependencies && packageContent.devDependencies['@playwright/test']) {
      success(`Playwright 依赖项: ${packageContent.devDependencies['@playwright/test']}`);
    } else {
      error('package.json 中缺少 @playwright/test 依赖项');
      return false;
    }

    return true;
  } catch (e) {
    error(`检查 package.json 时出错: ${e.message}`);
    return false;
  }
}

// 检查依赖项安装状态
function checkDependencies() {
  const nodeModulesPath = path.join(__dirname, 'node_modules');
  const playwrightPath = path.join(nodeModulesPath, '@playwright', 'test');

  if (fs.existsSync(nodeModulesPath)) {
    if (fs.existsSync(playwrightPath)) {
      success('Playwright 依赖项已安装');
      return true;
    } else {
      warning('node_modules 存在但缺少 Playwright');
      return false;
    }
  } else {
    warning('依赖项未安装 (node_modules 不存在)');
    return false;
  }
}

// 检查 Playwright 浏览器
function checkPlaywrightBrowsers() {
  try {
    execSync('npx playwright --version', { stdio: 'pipe' });
    success('Playwright CLI 可用');
    
    // 尝试检查浏览器状态
    try {
      const output = execSync('npx playwright install --dry-run', { 
        stdio: 'pipe',
        encoding: 'utf8'
      });
      
      if (output.includes('is already installed')) {
        success('Playwright 浏览器已安装');
        return true;
      } else {
        warning('Playwright 浏览器可能未安装');
        info('运行 "npm run install:browsers" 来安装浏览器');
        return false;
      }
    } catch (e) {
      warning('无法检查浏览器安装状态');
      return false;
    }
  } catch (e) {
    error('Playwright CLI 不可用');
    return false;
  }
}

// 验证导航脚本内容
function validateNavigationScript() {
  try {
    const scriptPath = path.join(__dirname, '80.lv Articles Page Navigation.js');
    if (!fs.existsSync(scriptPath)) {
      error('导航脚本文件不存在');
      return false;
    }

    const scriptContent = fs.readFileSync(scriptPath, 'utf8');
    
    // 检查关键选择器
    const requiredSelectors = [
      'paginationContainer: \'ul._2pSuN\'',
      'pageLink: \'ul._2pSuN li.E2y4M a._2ZymR\'',
      'activePageLink: \'li.E2y4M._3NLRK a._2ZymR\'',
      'sitePrevButton:',
      'siteNextButton:'
    ];

    const missingSelectors = requiredSelectors.filter(selector => !scriptContent.includes(selector));
    
    if (missingSelectors.length === 0) {
      success('导航脚本包含所有必要的选择器');
    } else {
      warning(`导航脚本可能缺少选择器: ${missingSelectors.length} 个`);
    }

    // 检查脚本版本
    const versionMatch = scriptContent.match(/@version\s+(\d+\.\d+)/);
    if (versionMatch) {
      info(`导航脚本版本: ${versionMatch[1]}`);
    }

    return true;
  } catch (e) {
    error(`验证导航脚本时出错: ${e.message}`);
    return false;
  }
}

// 生成设置建议
function generateSetupSuggestions(issues) {
  if (issues.length === 0) {
    return;
  }

  console.log('\n' + '='.repeat(60));
  log('yellow', '设置建议', '💡');
  console.log('='.repeat(60));

  issues.forEach((issue, index) => {
    console.log(`${index + 1}. ${issue}`);
  });
}

// 主验证函数
function main() {
  header('80.lv 分页导航测试环境验证');
  
  const issues = [];
  let allChecksPass = true;

  // 检查 Node.js
  console.log('\n📦 检查 Node.js 环境...');
  if (!checkNodeVersion()) {
    allChecksPass = false;
    issues.push('升级 Node.js 到 16.0.0 或更高版本');
  }

  // 检查文件
  console.log('\n📁 检查必要文件...');
  if (!checkRequiredFiles()) {
    allChecksPass = false;
    issues.push('确保所有必要文件都存在于项目目录中');
  }

  // 检查 package.json
  console.log('\n📋 检查项目配置...');
  if (!checkPackageJson()) {
    allChecksPass = false;
    issues.push('修复 package.json 配置问题');
  }

  // 检查依赖项
  console.log('\n📚 检查依赖项...');
  if (!checkDependencies()) {
    allChecksPass = false;
    issues.push('运行 "npm install" 安装依赖项');
  }

  // 检查 Playwright 浏览器
  console.log('\n🌐 检查 Playwright 浏览器...');
  if (!checkPlaywrightBrowsers()) {
    allChecksPass = false;
    issues.push('运行 "npm run install:browsers" 安装 Playwright 浏览器');
  }

  // 验证导航脚本
  console.log('\n🔧 验证导航脚本...');
  validateNavigationScript();

  // 总结
  console.log('\n' + '='.repeat(60));
  if (allChecksPass) {
    success('环境验证完成！所有检查都通过了');
    info('您可以运行 "npm test" 开始测试');
  } else {
    error('环境验证发现问题，请查看上面的详细信息');
    generateSetupSuggestions(issues);
  }
  console.log('='.repeat(60));

  return allChecksPass;
}

// 运行验证
if (require.main === module) {
  const success = main();
  process.exit(success ? 0 : 1);
}

module.exports = { main };
