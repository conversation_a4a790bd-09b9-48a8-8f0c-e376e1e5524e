# 80.lv 文章页面分页导航自动化测试

这是一个使用 Playwright 创建的自动化测试脚本，用于测试 `80.lv Articles Page Navigation.js` 文件中的分页导航功能。

## 功能特性

### 测试覆盖范围

1. **基本分页导航测试**
   - 验证当前页面状态
   - 导航到第二页
   - 验证"上一页"按钮的可见性和可点击性
   - 点击"上一页"按钮并验证返回第一页
   - 验证页面内容和按钮状态的正确更新

2. **快捷键导航测试**
   - 测试右箭头键（→）导航到下一页
   - 测试左箭头键（←）返回上一页
   - 验证快捷键导航的准确性

3. **超快速跳转功能测试**
   - 测试 Alt+g 快捷键聚焦输入框
   - 测试页面跳转功能
   - 验证跳转结果的准确性

### 测试特点

- **稳定性保证**：包含适当的等待机制和超时设置
- **详细日志**：提供详细的测试步骤日志和浏览器控制台输出
- **错误处理**：包含失败时的截图和视频录制
- **多浏览器支持**：支持 Chromium、Firefox 和 WebKit

## 安装和设置

### 前置要求

- Node.js 16.0.0 或更高版本
- npm 或 yarn 包管理器

### 安装步骤

1. **安装依赖项**
   ```bash
   npm install
   ```

2. **安装 Playwright 浏览器**
   ```bash
   npm run install:browsers
   ```

## 运行测试

### 基本测试命令

```bash
# 运行所有测试（无头模式）
npm test

# 运行测试并显示浏览器窗口
npm run test:headed

# 调试模式运行测试
npm run test:debug

# 使用 UI 模式运行测试
npm run test:ui

# 查看测试报告
npm run test:report
```

### 高级测试选项

```bash
# 运行特定测试文件
npx playwright test test-pagination-navigation.spec.js

# 运行特定测试用例
npx playwright test --grep "测试分页导航功能"

# 在特定浏览器中运行
npx playwright test --project=chromium

# 生成详细报告
npx playwright test --reporter=html
```

## 测试文件结构

```
├── 80.lv Articles Page Navigation.js  # 原始导航脚本
├── test-pagination-navigation.spec.js # 主要测试文件
├── playwright.config.js               # Playwright 配置
├── package.json                       # 项目依赖配置
└── README.md                          # 说明文档
```

## 测试流程详解

### 测试1：基本分页导航功能

1. **初始化阶段**
   - 导航到 80.lv/articles 页面
   - 注入导航脚本
   - 等待脚本初始化完成

2. **验证第一页状态**
   - 检查当前页面元素（li.E2y4M._3NLRK）
   - 确认显示为第1页

3. **导航到第二页**
   - 查找第2页链接
   - 点击第2页链接
   - 验证页面更新

4. **测试上一页按钮**
   - 验证原生上一页按钮可见且可点击
   - 验证脚本UI中的上一页按钮状态
   - 点击上一页按钮

5. **验证返回第一页**
   - 确认当前页面为第1页
   - 验证上一页按钮在第1页时被禁用

### 测试2：快捷键导航

- 使用 ArrowRight 键导航到下一页
- 使用 ArrowLeft 键返回上一页
- 验证快捷键导航的准确性

### 测试3：超快速跳转

- 使用 Alt+g 聚焦输入框
- 输入目标页码
- 点击跳转按钮
- 验证跳转结果

## 配置说明

### Playwright 配置特点

- **超时设置**：30秒的操作和导航超时
- **重试机制**：失败时不自动重试（可根据需要调整）
- **报告格式**：HTML、列表和JSON格式报告
- **调试支持**：失败时自动截图和录制视频

### 浏览器设置

- **视窗大小**：1280x720
- **用户代理**：模拟 Chrome 浏览器
- **HTTPS**：忽略HTTPS错误以确保测试稳定性

## 故障排除

### 常见问题

1. **页面加载超时**
   - 检查网络连接
   - 增加 `navigationTimeout` 设置

2. **元素未找到**
   - 验证选择器是否正确
   - 检查页面结构是否发生变化

3. **脚本注入失败**
   - 确保 `80.lv Articles Page Navigation.js` 文件存在
   - 检查文件路径是否正确

### 调试技巧

1. **启用有头模式**
   ```bash
   npm run test:headed
   ```

2. **使用调试模式**
   ```bash
   npm run test:debug
   ```

3. **查看详细日志**
   - 测试会输出详细的步骤日志
   - 浏览器控制台消息会被记录

## 扩展和自定义

### 添加新测试用例

在 `test-pagination-navigation.spec.js` 中添加新的 `test()` 块：

```javascript
test('自定义测试用例', async () => {
  // 测试逻辑
});
```

### 修改配置

编辑 `playwright.config.js` 来调整：
- 超时设置
- 浏览器选项
- 报告格式
- 并行执行设置

## 注意事项

1. **网络依赖**：测试需要访问 80.lv 网站，确保网络连接稳定
2. **页面结构**：如果网站更新页面结构，可能需要更新选择器
3. **性能考虑**：测试包含适当的等待时间，但可根据网络条件调整
4. **浏览器兼容性**：主要在 Chromium 中测试，其他浏览器可能需要额外配置

## 许可证

MIT License
