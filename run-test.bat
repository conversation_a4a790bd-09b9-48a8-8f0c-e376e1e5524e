@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: 设置颜色代码
set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "MAGENTA=[95m"
set "CYAN=[96m"
set "WHITE=[97m"
set "RESET=[0m"

:: 显示标题
echo.
echo %CYAN%================================================================%RESET%
echo %WHITE%🎭 80.lv 分页导航 Playwright 测试运行器 (Windows)%RESET%
echo %CYAN%================================================================%RESET%
echo.

:: 检查 Node.js 是否安装
node --version >nul 2>&1
if errorlevel 1 (
    echo %RED%❌ 错误: 未找到 Node.js，请先安装 Node.js%RESET%
    echo %YELLOW%   下载地址: https://nodejs.org/%RESET%
    pause
    exit /b 1
)

:: 检查必要文件
echo %BLUE%🔍 检查必要文件...%RESET%
set "missing_files="

if not exist "80.lv Articles Page Navigation.js" (
    set "missing_files=!missing_files! 80.lv Articles Page Navigation.js"
)
if not exist "test-pagination-navigation.spec.js" (
    set "missing_files=!missing_files! test-pagination-navigation.spec.js"
)
if not exist "playwright.config.js" (
    set "missing_files=!missing_files! playwright.config.js"
)
if not exist "package.json" (
    set "missing_files=!missing_files! package.json"
)

if not "!missing_files!"=="" (
    echo %RED%❌ 缺少必要文件:!missing_files!%RESET%
    pause
    exit /b 1
)

echo %GREEN%✅ 所有必要文件都存在%RESET%

:: 检查依赖是否已安装
if not exist "node_modules" (
    echo %YELLOW%⚠️  依赖项未安装，正在安装...%RESET%
    npm install
    if errorlevel 1 (
        echo %RED%❌ 依赖安装失败%RESET%
        pause
        exit /b 1
    )
)

:menu
echo.
echo %CYAN%请选择要运行的测试类型:%RESET%
echo.
echo 1. %GREEN%🏃 运行所有测试 (无头模式)%RESET%
echo 2. %GREEN%👀 运行测试 (显示浏览器)%RESET%
echo 3. %GREEN%🐛 调试模式运行测试%RESET%
echo 4. %GREEN%🎨 UI 模式运行测试%RESET%
echo 5. %GREEN%📊 查看测试报告%RESET%
echo 6. %GREEN%🔧 安装 Playwright 浏览器%RESET%
echo 7. %GREEN%📋 运行特定测试用例%RESET%
echo 8. %GREEN%🚀 快速开始 (安装浏览器 + 运行测试)%RESET%
echo 9. %RED%❌ 退出%RESET%
echo.

set /p choice=%YELLOW%请输入选项 (1-9): %RESET%

if "%choice%"=="1" (
    echo %GREEN%🏃 运行所有测试 (无头模式)...%RESET%
    npx playwright test
    goto continue
)

if "%choice%"=="2" (
    echo %GREEN%👀 运行测试 (显示浏览器)...%RESET%
    npx playwright test --headed
    goto continue
)

if "%choice%"=="3" (
    echo %GREEN%🐛 调试模式运行测试...%RESET%
    npx playwright test --debug
    goto continue
)

if "%choice%"=="4" (
    echo %GREEN%🎨 UI 模式运行测试...%RESET%
    npx playwright test --ui
    goto continue
)

if "%choice%"=="5" (
    echo %GREEN%📊 查看测试报告...%RESET%
    npx playwright show-report
    goto continue
)

if "%choice%"=="6" (
    echo %GREEN%🔧 安装 Playwright 浏览器...%RESET%
    npx playwright install
    goto continue
)

if "%choice%"=="7" (
    goto specific_tests
)

if "%choice%"=="8" (
    echo %GREEN%🚀 快速开始...%RESET%
    echo %CYAN%正在安装 Playwright 浏览器...%RESET%
    npx playwright install
    if errorlevel 1 (
        echo %RED%❌ 浏览器安装失败%RESET%
        goto continue
    )
    echo %CYAN%正在运行测试...%RESET%
    npx playwright test --headed
    goto continue
)

if "%choice%"=="9" (
    echo %GREEN%👋 再见!%RESET%
    exit /b 0
)

echo %RED%❌ 无效选项，请重新选择%RESET%
goto menu

:specific_tests
echo.
echo %CYAN%📋 可用的测试用例:%RESET%
echo.
echo 1. 测试分页导航功能：导航到第二页并验证上一页按钮功能
echo 2. 测试快捷键导航功能
echo 3. 测试超快速跳转功能
echo 4. 返回主菜单
echo.

set /p test_choice=%YELLOW%请输入选项 (1-4): %RESET%

if "%test_choice%"=="1" (
    echo %GREEN%🧪 运行分页导航功能测试...%RESET%
    npx playwright test --grep "测试分页导航功能：导航到第二页并验证上一页按钮功能"
    goto continue
)

if "%test_choice%"=="2" (
    echo %GREEN%⌨️ 运行快捷键导航功能测试...%RESET%
    npx playwright test --grep "测试快捷键导航功能"
    goto continue
)

if "%test_choice%"=="3" (
    echo %GREEN%🚀 运行超快速跳转功能测试...%RESET%
    npx playwright test --grep "测试超快速跳转功能"
    goto continue
)

if "%test_choice%"=="4" (
    goto menu
)

echo %RED%❌ 无效选项，请重新选择%RESET%
goto specific_tests

:continue
echo.
echo %YELLOW%按任意键继续...%RESET%
pause >nul
goto menu
