const { test, expect } = require('@playwright/test');
const fs = require('fs');
const path = require('path');

// 读取导航脚本内容
const navigationScript = fs.readFileSync(path.join(__dirname, '80.lv Articles Page Navigation.js'), 'utf8');

test.describe('80.lv 文章页面分页导航测试', () => {
  let page;

  test.beforeEach(async ({ browser }) => {
    // 创建新的浏览器上下文和页面
    const context = await browser.newContext();
    page = await context.newPage();
    
    // 启用控制台日志记录
    page.on('console', msg => {
      if (msg.type() === 'log' || msg.type() === 'error' || msg.type() === 'warn') {
        console.log(`浏览器控制台 [${msg.type()}]: ${msg.text()}`);
      }
    });

    // 导航到 80.lv 文章页面
    await page.goto('https://80.lv/articles', { 
      waitUntil: 'networkidle',
      timeout: 30000 
    });

    // 等待页面完全加载
    await page.waitForLoadState('domcontentloaded');
    await page.waitForTimeout(2000);

    // 注入导航脚本
    await page.addScriptTag({ content: navigationScript });
    
    // 等待脚本初始化
    await page.waitForTimeout(3000);
    
    console.log('✅ 导航脚本已注入并初始化');
  });

  test('测试分页导航功能：导航到第二页并验证上一页按钮功能', async () => {
    // 步骤1: 验证当前在第一页
    console.log('🔍 步骤1: 验证当前在第一页');
    
    // 等待分页容器出现
    await page.waitForSelector('ul._2pSuN', { timeout: 10000 });
    
    // 检查当前页面状态
    const currentPageElement = await page.locator('li.E2y4M._3NLRK a._2ZymR').first();
    await expect(currentPageElement).toBeVisible();
    
    const currentPageText = await currentPageElement.textContent();
    console.log(`当前页面: ${currentPageText}`);
    expect(currentPageText.trim()).toBe('1');

    // 步骤2: 导航到第二页
    console.log('🔍 步骤2: 导航到第二页');
    
    // 查找并点击第二页链接
    const secondPageLink = await page.locator('ul._2pSuN li.E2y4M a._2ZymR').filter({ hasText: '2' }).first();
    await expect(secondPageLink).toBeVisible();
    await secondPageLink.click();
    
    // 等待页面更新
    await page.waitForTimeout(2000);
    
    // 验证已成功导航到第二页
    const newCurrentPageElement = await page.locator('li.E2y4M._3NLRK a._2ZymR').first();
    await expect(newCurrentPageElement).toBeVisible();
    
    const newCurrentPageText = await newCurrentPageElement.textContent();
    console.log(`导航后的当前页面: ${newCurrentPageText}`);
    expect(newCurrentPageText.trim()).toBe('2');

    // 步骤3: 验证"上一页"按钮的可见性和可点击性
    console.log('🔍 步骤3: 验证"上一页"按钮的可见性和可点击性');
    
    // 查找网站原生的上一页按钮
    const sitePrevButton = await page.locator('ul._2pSuN > li:first-child > *._2ZymR').first();
    await expect(sitePrevButton).toBeVisible();
    
    // 检查按钮是否可点击（不是禁用状态）
    const prevButtonParentLi = await page.locator('ul._2pSuN > li:first-child').first();
    const hasDisabledClass = await prevButtonParentLi.evaluate(el => el.classList.contains('_25VMy'));
    
    console.log(`上一页按钮是否禁用: ${hasDisabledClass}`);
    expect(hasDisabledClass).toBe(false);

    // 验证脚本创建的UI控件中的上一页按钮
    const scriptPrevButton = await page.locator('#tm-page-prev-button-80lv-v28');
    await expect(scriptPrevButton).toBeVisible();
    
    const isScriptButtonDisabled = await scriptPrevButton.isDisabled();
    console.log(`脚本上一页按钮是否禁用: ${isScriptButtonDisabled}`);
    expect(isScriptButtonDisabled).toBe(false);

    // 步骤4: 点击"上一页"按钮
    console.log('🔍 步骤4: 点击"上一页"按钮');
    
    // 使用脚本的上一页按钮进行测试
    await scriptPrevButton.click();
    
    // 等待页面更新
    await page.waitForTimeout(2000);

    // 步骤5: 验证页面是否成功跳转回第一页
    console.log('🔍 步骤5: 验证页面是否成功跳转回第一页');
    
    // 检查当前页面是否为第一页
    const finalCurrentPageElement = await page.locator('li.E2y4M._3NLRK a._2ZymR').first();
    await expect(finalCurrentPageElement).toBeVisible();
    
    const finalCurrentPageText = await finalCurrentPageElement.textContent();
    console.log(`点击上一页后的当前页面: ${finalCurrentPageText}`);
    expect(finalCurrentPageText.trim()).toBe('1');

    // 步骤6: 验证URL和页面内容是否正确更新
    console.log('🔍 步骤6: 验证URL和页面内容是否正确更新');
    
    // 80.lv 使用 JavaScript 分页，URL 可能不会改变，但我们可以验证页面内容
    const currentUrl = page.url();
    console.log(`当前URL: ${currentUrl}`);
    
    // 验证分页控件状态
    const firstPagePrevButtonParentLi = await page.locator('ul._2pSuN > li:first-child').first();
    const isFirstPagePrevDisabled = await firstPagePrevButtonParentLi.evaluate(el => el.classList.contains('_25VMy'));
    
    console.log(`在第一页时上一页按钮是否禁用: ${isFirstPagePrevDisabled}`);
    expect(isFirstPagePrevDisabled).toBe(true);

    // 验证脚本UI中的按钮状态也相应更新
    const finalScriptPrevButtonDisabled = await scriptPrevButton.isDisabled();
    console.log(`脚本上一页按钮在第一页时是否禁用: ${finalScriptPrevButtonDisabled}`);
    expect(finalScriptPrevButtonDisabled).toBe(true);

    console.log('✅ 所有测试步骤完成，分页导航功能正常工作！');
  });

  test('测试快捷键导航功能', async () => {
    console.log('🔍 测试快捷键导航功能');
    
    // 等待分页容器出现
    await page.waitForSelector('ul._2pSuN', { timeout: 10000 });
    
    // 验证当前在第一页
    const currentPageElement = await page.locator('li.E2y4M._3NLRK a._2ZymR').first();
    const currentPageText = await currentPageElement.textContent();
    expect(currentPageText.trim()).toBe('1');
    
    // 使用右箭头键导航到下一页
    await page.keyboard.press('ArrowRight');
    await page.waitForTimeout(2000);
    
    // 验证已导航到第二页
    const newCurrentPageElement = await page.locator('li.E2y4M._3NLRK a._2ZymR').first();
    const newCurrentPageText = await newCurrentPageElement.textContent();
    expect(newCurrentPageText.trim()).toBe('2');
    
    // 使用左箭头键返回上一页
    await page.keyboard.press('ArrowLeft');
    await page.waitForTimeout(2000);
    
    // 验证已返回第一页
    const finalCurrentPageElement = await page.locator('li.E2y4M._3NLRK a._2ZymR').first();
    const finalCurrentPageText = await finalCurrentPageElement.textContent();
    expect(finalCurrentPageText.trim()).toBe('1');
    
    console.log('✅ 快捷键导航功能测试完成');
  });

  test('测试超快速跳转功能', async () => {
    console.log('🔍 测试超快速跳转功能');
    
    // 等待分页容器和脚本UI出现
    await page.waitForSelector('ul._2pSuN', { timeout: 10000 });
    await page.waitForSelector('#tm-page-nav-container-80lv-v28', { timeout: 5000 });
    
    // 使用 Alt+g 快捷键聚焦输入框
    await page.keyboard.press('Alt+g');
    await page.waitForTimeout(500);
    
    // 输入目标页面（假设跳转到第3页）
    const pageInput = await page.locator('#tm-page-input-80lv-v28');
    await expect(pageInput).toBeFocused();
    
    await pageInput.fill('3');
    
    // 点击跳转按钮
    const goButton = await page.locator('#tm-page-go-button-80lv-v28');
    await goButton.click();
    
    // 等待导航完成
    await page.waitForTimeout(3000);
    
    // 验证已跳转到第3页
    const currentPageElement = await page.locator('li.E2y4M._3NLRK a._2ZymR').first();
    const currentPageText = await currentPageElement.textContent();
    console.log(`跳转后的当前页面: ${currentPageText}`);
    
    // 注意：如果第3页不存在，脚本会跳转到最大可用页面
    const pageNumber = parseInt(currentPageText.trim());
    expect(pageNumber).toBeGreaterThanOrEqual(1);
    
    console.log('✅ 超快速跳转功能测试完成');
  });

  test.afterEach(async () => {
    if (page) {
      await page.close();
    }
  });
});
