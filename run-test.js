#!/usr/bin/env node

/**
 * 80.lv 分页导航测试运行器
 * 这个脚本提供了一个简单的界面来运行不同类型的测试
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// 颜色输出函数
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function colorLog(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 检查必要文件是否存在
function checkRequiredFiles() {
  const requiredFiles = [
    '80.lv Articles Page Navigation.js',
    'test-pagination-navigation.spec.js',
    'playwright.config.js',
    'package.json'
  ];

  const missingFiles = requiredFiles.filter(file => !fs.existsSync(path.join(__dirname, file)));
  
  if (missingFiles.length > 0) {
    colorLog('red', '❌ 缺少必要文件:');
    missingFiles.forEach(file => colorLog('red', `   - ${file}`));
    process.exit(1);
  }
  
  colorLog('green', '✅ 所有必要文件都存在');
}

// 检查依赖是否已安装
function checkDependencies() {
  if (!fs.existsSync(path.join(__dirname, 'node_modules'))) {
    colorLog('yellow', '⚠️  依赖项未安装，正在安装...');
    return runCommand('npm', ['install']);
  }
  return Promise.resolve();
}

// 运行命令的辅助函数
function runCommand(command, args, options = {}) {
  return new Promise((resolve, reject) => {
    colorLog('cyan', `🚀 运行命令: ${command} ${args.join(' ')}`);
    
    const child = spawn(command, args, {
      stdio: 'inherit',
      shell: true,
      ...options
    });

    child.on('close', (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`命令失败，退出码: ${code}`));
      }
    });

    child.on('error', (error) => {
      reject(error);
    });
  });
}

// 显示菜单
function showMenu() {
  console.log('\n' + '='.repeat(60));
  colorLog('bright', '🎭 80.lv 分页导航 Playwright 测试运行器');
  console.log('='.repeat(60));
  console.log('');
  colorLog('cyan', '请选择要运行的测试类型:');
  console.log('');
  console.log('1. 🏃 运行所有测试 (无头模式)');
  console.log('2. 👀 运行测试 (显示浏览器)');
  console.log('3. 🐛 调试模式运行测试');
  console.log('4. 🎨 UI 模式运行测试');
  console.log('5. 📊 查看测试报告');
  console.log('6. 🔧 安装 Playwright 浏览器');
  console.log('7. 📋 运行特定测试用例');
  console.log('8. ❌ 退出');
  console.log('');
}

// 获取用户输入
function getUserInput() {
  return new Promise((resolve) => {
    process.stdin.once('data', (data) => {
      resolve(data.toString().trim());
    });
  });
}

// 显示测试用例菜单
function showTestCases() {
  console.log('\n' + '-'.repeat(50));
  colorLog('cyan', '📋 可用的测试用例:');
  console.log('');
  console.log('1. 测试分页导航功能：导航到第二页并验证上一页按钮功能');
  console.log('2. 测试快捷键导航功能');
  console.log('3. 测试超快速跳转功能');
  console.log('4. 返回主菜单');
  console.log('');
}

// 主函数
async function main() {
  try {
    colorLog('blue', '🔍 检查环境...');
    checkRequiredFiles();
    await checkDependencies();
    
    while (true) {
      showMenu();
      colorLog('yellow', '请输入选项 (1-8): ');
      
      const choice = await getUserInput();
      
      switch (choice) {
        case '1':
          colorLog('green', '🏃 运行所有测试 (无头模式)...');
          await runCommand('npx', ['playwright', 'test']);
          break;
          
        case '2':
          colorLog('green', '👀 运行测试 (显示浏览器)...');
          await runCommand('npx', ['playwright', 'test', '--headed']);
          break;
          
        case '3':
          colorLog('green', '🐛 调试模式运行测试...');
          await runCommand('npx', ['playwright', 'test', '--debug']);
          break;
          
        case '4':
          colorLog('green', '🎨 UI 模式运行测试...');
          await runCommand('npx', ['playwright', 'test', '--ui']);
          break;
          
        case '5':
          colorLog('green', '📊 查看测试报告...');
          await runCommand('npx', ['playwright', 'show-report']);
          break;
          
        case '6':
          colorLog('green', '🔧 安装 Playwright 浏览器...');
          await runCommand('npx', ['playwright', 'install']);
          break;
          
        case '7':
          await handleSpecificTestCase();
          break;
          
        case '8':
          colorLog('green', '👋 再见!');
          process.exit(0);
          break;
          
        default:
          colorLog('red', '❌ 无效选项，请重新选择');
          break;
      }
      
      if (choice !== '8') {
        colorLog('yellow', '\n按 Enter 键继续...');
        await getUserInput();
      }
    }
  } catch (error) {
    colorLog('red', `❌ 错误: ${error.message}`);
    process.exit(1);
  }
}

// 处理特定测试用例
async function handleSpecificTestCase() {
  while (true) {
    showTestCases();
    colorLog('yellow', '请输入选项 (1-4): ');
    
    const choice = await getUserInput();
    
    switch (choice) {
      case '1':
        colorLog('green', '🧪 运行分页导航功能测试...');
        await runCommand('npx', ['playwright', 'test', '--grep', '测试分页导航功能：导航到第二页并验证上一页按钮功能']);
        return;
        
      case '2':
        colorLog('green', '⌨️ 运行快捷键导航功能测试...');
        await runCommand('npx', ['playwright', 'test', '--grep', '测试快捷键导航功能']);
        return;
        
      case '3':
        colorLog('green', '🚀 运行超快速跳转功能测试...');
        await runCommand('npx', ['playwright', 'test', '--grep', '测试超快速跳转功能']);
        return;
        
      case '4':
        return;
        
      default:
        colorLog('red', '❌ 无效选项，请重新选择');
        break;
    }
  }
}

// 设置输入模式
process.stdin.setRawMode(false);
process.stdin.resume();
process.stdin.setEncoding('utf8');

// 运行主函数
main().catch((error) => {
  colorLog('red', `❌ 未处理的错误: ${error.message}`);
  process.exit(1);
});
